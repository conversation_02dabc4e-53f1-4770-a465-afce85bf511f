{"name": "@minimal-kit/next-js", "author": "Minimals", "version": "7.0.0", "description": "Next & JavaScript", "private": true, "scripts": {"dev": "next dev -p 3034 --turbopack", "start": "next start -p 3034", "build": "next build", "postbuild": "next-sitemap", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "sitemap:generate": "next-sitemap", "seo:check": "echo 'Checking SEO configuration...' && curl -I http://localhost:3034/robots.txt && curl -I http://localhost:3034/sitemap.xml", "clean": "rm -rf node_modules .next out dist build", "re:dev": "yarn clean && yarn install && yarn dev", "re:build": "yarn clean && yarn install && yarn build", "re:build-npm": "npm run clean && npm install && npm run build"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22", "dependencies": {"@ai-sdk/google": "^1.2.18", "@ai-sdk/react": "^1.2.12", "@auth0/auth0-react": "^2.3.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource-variable/dm-sans": "^5.2.5", "@fontsource-variable/inter": "^5.2.5", "@fontsource-variable/nunito-sans": "^5.2.5", "@fontsource-variable/public-sans": "^5.2.5", "@fontsource/barlow": "^5.2.5", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/timeline": "^6.1.15", "@hookform/resolvers": "^4.1.3", "@iconify/react": "^5.2.0", "@mui/lab": "^7.0.0-beta.10", "@mui/material": "^7.0.1", "@mui/material-nextjs": "^7.0.0", "@mui/x-data-grid": "^7.28.2", "@mui/x-date-pickers": "^7.28.2", "@mui/x-tree-view": "^7.28.1", "@next/third-parties": "^15.3.3", "@payos/payos-checkout": "^1.0.8", "@react-pdf/renderer": "^4.3.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.3", "@tiptap/core": "^2.11.6", "@tiptap/extension-code-block": "^2.11.6", "@tiptap/extension-code-block-lowlight": "^2.11.6", "@tiptap/extension-image": "^2.11.6", "@tiptap/extension-link": "^2.11.6", "@tiptap/extension-placeholder": "^2.11.6", "@tiptap/extension-text-align": "^2.11.6", "@tiptap/extension-underline": "^2.11.6", "@tiptap/pm": "^2.11.6", "@tiptap/react": "^2.11.6", "@tiptap/starter-kit": "^2.11.6", "ai": "^4.3.16", "apexcharts": "^4.5.0", "autosuggest-highlight": "^3.3.4", "aws-amplify": "^6.14.1", "axios": "^1.8.4", "dayjs": "^1.11.13", "embla-carousel": "^8.5.2", "embla-carousel-auto-height": "^8.5.2", "embla-carousel-auto-scroll": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-fade": "^8.5.2", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.34.1", "file-saver": "^2.0.5", "firebase": "^11.5.0", "framer-motion": "^12.6.1", "html2canvas": "^1.4.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "jspdf": "^3.0.1", "lowlight": "^3.3.0", "mapbox-gl": "^3.10.0", "minimal-shared": "^1.0.7", "mui-one-time-password-input": "^4.0.1", "next": "^15.2.4", "next-sitemap": "^4.2.3", "nprogress": "^0.2.0", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-map-gl": "^8.0.2", "react-markdown": "^10.1.0", "react-organizational-chart": "^2.2.1", "react-phone-number-input": "^3.4.12", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "schema-dts": "^1.1.5", "simplebar-react": "^3.3.0", "sonner": "^2.0.2", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.3", "turndown": "^7.2.0", "yet-another-react-lightbox": "^3.21.8", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.23.0", "@svgr/webpack": "^8.1.0", "eslint": "^9.23.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.10.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "prettier": "^3.5.3"}}